import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

import pandas as pd
import matplotlib.pyplot as plt
from indicators.macd import macd
from utilities.utils import load_stock_data
import matplotlib.dates as mdates

# 数据目录和股票代码
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'ashare', 'daily_hfq')
STOCK_CODE = '600519'

# 设置起止日期
START_DATE = '2024-01-01'  # 修改为你想要的起始日期
END_DATE = ''    # 修改为你想要的结束日期

# 1. 加载股票数据
df_display, df_with_history, title_prefix = load_stock_data(STOCK_CODE, DATA_DIR, START_DATE, END_DATE)

if df is None:
    exit(1)

# 获取实际起止日期字符串
actual_start = df_display['日期'].iloc[0].strftime('%Y.%m.%d')
actual_end = df_display['日期'].iloc[-1].strftime('%Y.%m.%d')
period_str = f"{actual_start}-{actual_end}"

# 2. 计算MACD
if '收盘' not in df.columns:
    print("数据中缺少'收盘'列！")
    exit(1)

diff, dea, macd_val = macd(df_display['收盘'])

# 3. 可视化（分为上下两个子图）
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True, gridspec_kw={'height_ratios': [2, 1]})

# 上面：收盘价
ax1.plot(df_display['日期'], df_display['收盘'], label='Close', color='black')
ax1.set_title(f'{title_prefix} {period_str}', fontname='STHeiti', fontsize=16)
ax1.set_ylabel('Price')
ax1.legend()
ax1.twinx()

# 下面：MACD
ax2.plot(df_display['日期'], diff, label='DIFF', color='blue')
ax2.plot(df_display['日期'], dea, label='DEA', color='orange')
ax2.bar(df_display['日期'], macd_val, label='MACD', color='gray', alpha=0.5)
ax2.set_title('MACD')
ax2.set_xlabel('Date')
ax2.set_ylabel('Value')
ax2.legend()
ax2.twinx()

# 设置X轴只显示有数据的日期，并美化刻度
ax2.xaxis.set_major_locator(mdates.AutoDateLocator())
ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
fig.autofmt_xdate()

plt.tight_layout()
plt.show() 
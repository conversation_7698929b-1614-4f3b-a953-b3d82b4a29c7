import sys
import os
import pandas as pd
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utilities.utils import get_stock_name_from_csv

def fill_stock_code_zero_in_csv(csv_path):
    df = pd.read_csv(csv_path, dtype=str)
    # 检查常见的股票代码列名
    code_cols = [col for col in df.columns if col in ['股票代码', 'stock_code', 'code', '代码']]
    if not code_cols:
        print(f"未找到股票代码列: {csv_path}")
        return
    code_col = code_cols[0]
    # 补零
    df[code_col] = df[code_col].astype(str).str.zfill(6)
    df.to_csv(csv_path, index=False)
    print(f"已处理: {csv_path}")

def batch_fill_stock_code(directory):
    for fname in os.listdir(directory):
        if fname.endswith('.csv'):
            fpath = os.path.join(directory, fname)
            fill_stock_code_zero_in_csv(fpath)

if __name__ == "__main__":
    data_dir = "ashare/daily_hfq"
    batch_fill_stock_code(data_dir)

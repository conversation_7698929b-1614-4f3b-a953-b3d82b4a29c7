import os
import pandas as pd
from tqdm import tqdm
import re
import sys

def get_limit_series(df, stock_code):
    """
    根据股票代码和DataFrame内容，为每行数据生成对应的涨跌幅限制Series。
    """
    # 默认主板涨跌幅
    limit = 0.10
    ipo_days_to_skip = 1  # 主板跳过首日

    if stock_code.startswith('688'):  # 科创板
        limit = 0.20
        ipo_days_to_skip = 5
    elif stock_code.startswith('300'):  # 创业板
        limit = 0.20
        ipo_days_to_skip = 5
    elif stock_code.startswith(('8', '43')):  # 北交所
        limit = 0.30
        ipo_days_to_skip = 1

    limits = pd.Series(limit, index=df.index)

    # 创业板注册制改革，2020-08-24后涨跌幅变为20%
    if stock_code.startswith('300') and '日期' in df.columns:
        try:
            trade_date = pd.to_datetime(df['日期'], errors='coerce')
            reform_date = pd.to_datetime('2020-08-24')
            limits[trade_date < reform_date] = 0.10
        except Exception:
            # 日期格式问题，使用统一limit
            pass

    # ST/*ST 股票涨跌幅为5%
    if '名称' in df.columns:
        # is_st = df['名称'].str.contains('ST', na=False)
        # 考虑到 "GST" 这类非ST股，精确匹配 "ST" 或 "*ST" 开头
        is_st = df['名称'].str.match(r'(\*?ST)', na=False)
        limits[is_st] = 0.05

    # 新股上市初期无涨跌幅限制
    if ipo_days_to_skip > 0 and len(df) >= ipo_days_to_skip:
        limits.iloc[:ipo_days_to_skip] = float('inf')

    return limits

def check_price_limit_outliers(df, stock_code):
    """
    检查DataFrame中是否存在连续两日的涨跌幅异常值。
    过滤掉单日异常（如停牌复牌后的大幅波动）。
    返回一个标记了异常日的布尔Series，否则返回None。
    """
    if len(df) < 3:  # 需要至少3天数据才能判断连续两天异常
        return None

    limits = get_limit_series(df, stock_code)
    prev_close = df['收盘'].shift(1)

    # 避免除以0或空值
    prev_close = prev_close.replace(0, pd.NA).ffill()
    if prev_close.isna().all():
        return None
        
    # 涨跌停价格需要根据交易所规则进行四舍五入
    upper_bound = (prev_close * (1 + limits)).round(2)
    lower_bound = (prev_close * (1 - limits)).round(2)
    
    # 将价格转换为整数（乘以100）来避免浮点数精度问题
    highest_price_int = (df['最高'] * 100).round().astype(int)
    lowest_price_int = (df['最低'] * 100).round().astype(int)
    upper_bound_int = (upper_bound * 100).round().astype(int)
    lower_bound_int = (lower_bound * 100).round().astype(int)

    daily_outliers = ((highest_price_int > upper_bound_int) | (lowest_price_int < lower_bound_int)).iloc[1:]

    if not daily_outliers.any():
        return None

    # 检查是否存在连续两天的异常值
    consecutive_outliers_day2 = (daily_outliers & daily_outliers.shift(1).fillna(False))

    if consecutive_outliers_day2.any():
        # 我们需要标记连续异常的两天。
        # day2是连续异常的第二天。day1是其前一天。
        # daily_outliers中为True的索引，并且其后一天也是True的，就是第一天。
        consecutive_outliers_day1 = daily_outliers & daily_outliers.shift(-1).fillna(False)
        
        # 合并两天，得到所有异常日
        full_outlier_series = consecutive_outliers_day1 | consecutive_outliers_day2
        
        # 重新索引以匹配原始DataFrame的长度
        return full_outlier_series.reindex(df.index, fill_value=False)
    
    return None

def validate_data_files(directory, project_root, print_details=False):
    """
    扫描目录中的所有CSV文件，检查负价格和价格涨跌幅异常值。
    - 发现负价格文件后，将其对应的股票代码加入黑名单，然后删除文件。
    - 发现价格异常文件后，仅打印报告。
    如果 print_details 为 True，则打印第一个异常文件的详细信息并退出。
    """
    print(f"Scanning directory: {directory}")
    files_with_negative_prices = []
    files_with_outliers = []
    
    price_cols = ['开盘', '最高', '最低', '收盘']
    
    # 使用os.scandir以提高性能
    # try...except to handle case where directory doesn't exist
    try:
        file_iterator = list(os.scandir(directory))
    except FileNotFoundError:
        print(f"Error: Directory not found at {directory}")
        return

    for entry in tqdm(file_iterator, desc="Scanning CSV files"):
        if entry.name.endswith('.csv') and entry.is_file():
            try:
                # 从文件名提取股票代码
                match = re.search(r'\d{6}', entry.name)
                if not match:
                    continue
                stock_code = match.group(0)

                df = pd.read_csv(entry.path)
                
                # 检查必要的列是否存在
                if not all(col in df.columns for col in price_cols):
                    continue
                
                # 1. 检查负价格
                if (df[price_cols] < 0).any().any():
                    files_with_negative_prices.append(entry.path)
                    continue

                # 2. 检查价格涨跌幅异常值
                outlier_series = check_price_limit_outliers(df, stock_code)
                if outlier_series is not None and outlier_series.any():
                    files_with_outliers.append(entry.path)
                    
                    if print_details:
                        print(f"\n--- Outlier Details for {entry.path} ---")
                        # 获取异常行的索引
                        outlier_indices = df.index[outlier_series]
                        
                        # 为了提供上下文，我们打印异常行和它们各自的前一行
                        indices_to_print = set()
                        for idx in outlier_indices:
                            if idx > 0:
                                indices_to_print.add(idx - 1)  # 上下文：前一天
                            indices_to_print.add(idx)      # 异常当天
                        
                        sorted_indices = sorted(list(indices_to_print))
                        
                        # 准备要打印的列
                        columns_to_print = ['日期', '名称', '开盘', '收盘', '最高', '最低']
                        print_cols = [col for col in columns_to_print if col in df.columns]
                        print(df.loc[sorted_indices, print_cols])
                        return # 找到第一个就退出

            except Exception as e:
                # print(f"Could not read or process {entry.name}: {e}")
                pass

    print("\n--- Validation Report ---")
    
    if files_with_negative_prices:
        print(f"\nFound {len(files_with_negative_prices)} files with negative prices. Deleting them and updating blacklist...")
        
        blacklisted_codes = []
        for filepath in files_with_negative_prices:
            try:
                match = re.search(r'\d{6}', os.path.basename(filepath))
                if match:
                    blacklisted_codes.append(match.group(0))
                
                os.remove(filepath)
                print(f"- Deleted: {filepath}")
            except OSError as e:
                print(f"Error deleting file {filepath}: {e}")
        
        if blacklisted_codes:
            blacklist_path = os.path.join(project_root, 'ashare', 'blacklist.txt')
            existing_codes = set()
            try:
                if os.path.exists(blacklist_path):
                    with open(blacklist_path, 'r') as f:
                        existing_codes = set(line.strip() for line in f if line.strip())
            except IOError as e:
                print(f"Error reading blacklist file: {e}")


            new_codes_to_add = set(blacklisted_codes) - existing_codes
            if new_codes_to_add:
                try:
                    with open(blacklist_path, 'a') as f:
                        for code in sorted(list(new_codes_to_add)):
                            f.write(f"{code}\n")
                    print(f"\nAdded {len(new_codes_to_add)} new codes to {blacklist_path}")
                except IOError as e:
                    print(f"Error writing to blacklist file: {e}")
            else:
                print("\nBlacklist is already up to date.")

    if not files_with_negative_prices and not files_with_outliers:
        print("All files are clean. No negative prices or outliers found.")
    
    if files_with_outliers:
        print(f"\nFound {len(files_with_outliers)} files with price limit outliers (not deleted):")
        for filepath in files_with_outliers:
            print(f"- {filepath}")
    
    print("\nValidation complete.")


if __name__ == "__main__":
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    hfq_data_directory = os.path.join(project_root, 'ashare', 'daily_hfq')
    
    # 设置为True以打印第一个找到的异常文件的详细信息
    validate_data_files(hfq_data_directory, project_root, print_details=True) 


# strategies/RoC/run_2011_analysis.py
import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
import ffn
import numpy as np
from tqdm import tqdm

# --- Path Setup & Font ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utilities.utils_portfolio import run_portfolio_simulation
from utilities.complete_font_solution import setup_chinese_font
from utilities.utils import load_stock_name_mapping

def add_stock_code_prefix(df):
    """Adds 'sh.' or 'sz.' prefix to stock codes based on their first digits."""
    def get_prefix(code):
        if isinstance(code, str):
            if code.startswith('60') or code.startswith('688'):
                return f"sh.{code}"
            elif code.startswith('00') or code.startswith('300'):
                return f"sz.{code}"
            elif code.startswith('8') or code.startswith('43'):
                return f"bj.{code}"
        return code
    if 'StockCode' in df.columns:
        df['StockCode'] = df['StockCode'].astype(str).str.zfill(6)
        df['StockCode'] = df['StockCode'].apply(get_prefix)
    return df

def create_position_sizer(max_position_percent):
    if not 0 < max_position_percent <= 1:
        raise ValueError("max_position_percent must be between 0 and 1.")
    def position_sizer(total_nav, price_per_share, stock_code=None):
        return total_nav * max_position_percent
    return position_sizer

def get_stock_blacklist(project_root_path):
    blacklist_stocks = set()
    list_files = ['sh_stock_list.csv', 'sz_stock_list.csv', 'bj_stock_list.csv']
    for file_name in list_files:
        try:
            path = os.path.join(project_root_path, 'ashare', file_name)
            df = pd.read_csv(path, dtype={'证券代码': str})
            filtered_df = df[df['证券简称'].str.contains('ST|退', na=False, regex=True)]
            blacklist_stocks.update(filtered_df['证券代码'].str.zfill(6))
        except FileNotFoundError:
            print(f"Warning: Stock list file not found at {path}")
        except Exception as e:
            print(f"An error occurred while processing {file_name}: {e}")
    return blacklist_stocks

def run_portfolio_backtest(trades_df, market_data_df, benchmark_data_df, st_blacklist, max_position_percent, start_date, end_date, title_suffix="", show_plot=False):
    setup_chinese_font()
    INITIAL_CAPITAL = 1_000_000
    COMMISSION_RATE = 0.0003
    STAMP_DUTY_RATE = 0.0005

    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    trades_df = trades_df[(trades_df['EntryDate'] >= start_date) & (trades_df['EntryDate'] <= end_date)]

    if trades_df.empty:
        print(f"Trade log is empty for the period {start_date} to {end_date}. Skipping portfolio backtest.")
        return

    position_sizer_fn = create_position_sizer(max_position_percent)

    nav_history, completed_trades = run_portfolio_simulation(
        trades_df=trades_df,
        market_data_df=market_data_df.copy(),
        start_date=start_date,
        initial_capital=INITIAL_CAPITAL,
        st_blacklist=st_blacklist,
        commission_rate=COMMISSION_RATE,
        stamp_duty_rate=STAMP_DUTY_RATE,
        position_sizing_fn=position_sizer_fn
    )

    output_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
    os.makedirs(output_dir, exist_ok=True)

    if not completed_trades.empty:
        trades_output_path = os.path.join(output_dir, f'completed_trades{title_suffix and f"_{title_suffix}"}.csv')
        stock_name_map = load_stock_name_mapping(project_root)
        completed_trades['StockName'] = completed_trades['StockCode'].map(stock_name_map)
        completed_trades.to_csv(trades_output_path, index=False, encoding='utf-8-sig')
        print(f"\nCompleted trades log saved to: {trades_output_path}")

    if not nav_history.empty:
        nav_history_output_path = os.path.join(output_dir, f'nav_history{title_suffix and f"_{title_suffix}"}.csv')
        nav_history.to_csv(nav_history_output_path)
        print(f"NAV history saved to: {nav_history_output_path}")

    print("\n" + "="*80)
    print(f"      FFN Portfolio Backtest Results - {title_suffix.replace('_', ' ').title()}")
    print("="*80)

    benchmark_data_df = benchmark_data_df[benchmark_data_df.index >= nav_history.index[0]]
    benchmark_nav = (benchmark_data_df['Close'] / benchmark_data_df['Close'].iloc[0]) * INITIAL_CAPITAL
    benchmark_nav.name = '沪深300基准'
    strategy_nav = nav_history['NAV'].rename('策略')
    nav_data = ffn.merge(strategy_nav, benchmark_nav)
    stats = nav_data.calc_stats()
    stats.display()

    if show_plot:
        plot_path = os.path.join(output_dir, f'ffn_portfolio_performance{title_suffix and f"_{title_suffix}"}.png')
        ax = nav_data.plot(title=f"策略 vs. 沪深300基准 ({title_suffix.replace('_', ' ').title()})", figsize=(15, 10), logy=True)
        nav_history['PositionRatio'] = nav_history['PositionsValue'] / nav_history['NAV']
        ax2 = ax.twinx()
        ax2.fill_between(nav_history.index, nav_history['PositionRatio'], alpha=0.2, color='grey', label='仓位')
        ax2.set_ylabel('仓位比例 (Position Ratio)')
        ax2.set_ylim(0, 1.05)
        ax2.grid(False)
        lines, labels = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines + lines2, labels + labels2, loc='upper left')
        ax.figure.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"\nFFN 性能图表已保存至: {plot_path}")
        plt.show()

def main():
    print("Loading pre-generated trade signals...")
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    trades_df_path = os.path.join(project_root, 'strategies', 'RoC', 'backtest_results', 'full_market_trades_v2.csv')
    try:
        trades_df = pd.read_csv(trades_df_path)
    except FileNotFoundError:
        print(f"Error: Trade signals file not found at {trades_df_path}")
        return

    print("Loading full market OHLC data for simulation...")
    market_data_path = os.path.join(project_root, 'ashare', 'all_daily_hfq.parquet')
    market_data_df = pd.read_parquet(market_data_path)
    market_data_df = market_data_df.rename(columns={'日期': 'Date', '股票代码': 'StockCode', '收盘': 'Close'})
    market_data_df = add_stock_code_prefix(market_data_df)

    print("Loading benchmark data (CSI 300)...")
    benchmark_path = os.path.join(project_root, 'ashare', 'Indices_daily', 'sh000300.csv')
    benchmark_data_df = pd.read_csv(benchmark_path, index_col='日期', parse_dates=True)
    benchmark_data_df.rename(columns={'收盘': 'Close'}, inplace=True)

    print("Loading ST and '退' stock list to use as a blacklist...")
    blacklist_codes = get_stock_blacklist(project_root)
    blacklist_df = pd.DataFrame(list(blacklist_codes), columns=['StockCode'])
    blacklist_df = add_stock_code_prefix(blacklist_df)
    blacklist = set(blacklist_df['StockCode'].unique())

    initial_trade_count = len(trades_df)
    trades_df = trades_df[~trades_df['StockCode'].isin(blacklist)]
    print(f"Trades after removing blacklisted (ST/'退') stocks: {len(trades_df)} ({initial_trade_count - len(trades_df)} removed)")

    print("Calculating historical indicators (Volatility, EMA20) for filtering...")
    market_data_df['Date'] = pd.to_datetime(market_data_df['Date'])
    market_data_df['Returns'] = market_data_df.groupby('StockCode')['Close'].pct_change()
    market_data_df['Volatility'] = market_data_df.groupby('StockCode')['Returns'].transform(lambda x: x.rolling(window=250, min_periods=60).std() * np.sqrt(250))
    market_data_df['EMA20'] = market_data_df.groupby('StockCode')['Close'].transform(lambda x: x.ewm(span=20, adjust=False).mean())

    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    trades_df = pd.merge(trades_df, market_data_df[['Date', 'StockCode', 'Volatility', 'EMA20']], left_on=['EntryDate', 'StockCode'], right_on=['Date', 'StockCode'], how='left')
    trades_df.dropna(subset=['Volatility', 'EMA20'], inplace=True)

    volatility_threshold = 0.3
    trades_df = trades_df[trades_df['Volatility'] > volatility_threshold]
    trades_df['EntryPrice/EMA20'] = trades_df['EntryPrice'] / trades_df['EMA20']
    ema_ratio_threshold = 1.0
    trades_df = trades_df[trades_df['EntryPrice/EMA20'] <= ema_ratio_threshold]
    trades_df.drop(columns=['Date', 'Volatility', 'EMA20', 'EntryPrice/EMA20'], inplace=True, errors='ignore')

    # --- Configuration for 2010-2011 Analysis ---
    MAX_POSITION_PERCENT = 0.05
    ANALYSIS_START_DATE = '2010-01-01'
    ANALYSIS_END_DATE = '2011-12-31'

    print("\n" + "="*80)
    print(f"      Running 2010-2011 Historical Analysis ({ANALYSIS_START_DATE} to {ANALYSIS_END_DATE})")
    print("="*80)
    run_portfolio_backtest(
        trades_df=trades_df.copy(),
        market_data_df=market_data_df,
        benchmark_data_df=benchmark_data_df,
        st_blacklist=blacklist,
        max_position_percent=MAX_POSITION_PERCENT,
        start_date=ANALYSIS_START_DATE,
        end_date=ANALYSIS_END_DATE,
        title_suffix="2010_2011_analysis",
        show_plot=True
    )

if __name__ == "__main__":
    main()


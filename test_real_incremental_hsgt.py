#!/usr/bin/env python3
"""
使用真实数据测试增量港股通更新机制

使用实际的00001.csv文件来测试增量更新功能。
"""

import pandas as pd
import numpy as np
import os
import sys
import shutil
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, '/Users/<USER>/AI/Cursor/Futu')

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("❌ AKShare not available")
    exit(1)

# 导入修改后的函数
from data.get_daily_data_final import get_hsgt_data_for_stock, merge_hsgt_data_with_stock_data

def create_backup_and_simulate_missing_data():
    """创建备份并模拟缺失的港股通数据"""
    
    original_file = "/Users/<USER>/AI/Cursor/Futu/data/H_daily/00001.csv"
    backup_file = "/Users/<USER>/AI/Cursor/Futu/data/H_daily/00001_backup.csv"
    
    if not os.path.exists(original_file):
        print(f"❌ Original file not found: {original_file}")
        return None, None
    
    # 创建备份
    shutil.copy2(original_file, backup_file)
    print(f"📁 Created backup: {backup_file}")
    
    # 读取原始数据
    original_data = pd.read_csv(original_file)
    print(f"📊 Loaded {len(original_data)} records from original file")
    
    # 检查当前港股通数据状态
    current_hsgt_count = original_data['hsgt_date'].notna().sum()
    print(f"📈 Current HSGT records: {current_hsgt_count}")
    
    # 模拟删除最近几天的港股通数据
    modified_data = original_data.copy()
    
    # 找到最近有港股通数据的记录
    hsgt_records = modified_data[modified_data['hsgt_date'].notna()]
    if len(hsgt_records) == 0:
        print("❌ No HSGT data found in original file")
        return None, None
    
    # 删除最近5条港股通记录（模拟缺失）
    latest_hsgt_indices = hsgt_records.tail(5).index
    hsgt_columns = ['hsgt_date', 'hsgt_close_price', 'hsgt_change_rate',
                   'hsgt_holding_shares', 'hsgt_holding_value', 'hsgt_holding_ratio',
                   'hsgt_value_change_1d', 'hsgt_value_change_5d', 'hsgt_value_change_10d']
    
    for idx in latest_hsgt_indices:
        for col in hsgt_columns:
            if col in modified_data.columns:
                modified_data.loc[idx, col] = None
    
    # 显示模拟的缺失情况
    removed_dates = original_data.loc[latest_hsgt_indices, 'time_key'].str[:10].tolist()
    print(f"🔧 Simulated missing HSGT data for dates: {', '.join(removed_dates)}")
    
    new_hsgt_count = modified_data['hsgt_date'].notna().sum()
    print(f"📉 HSGT records after simulation: {new_hsgt_count} (removed {current_hsgt_count - new_hsgt_count})")
    
    return original_data, modified_data

def test_incremental_vs_full_update(original_data, modified_data):
    """比较增量更新和全量更新的效果"""
    
    print(f"\n🔄 Testing Incremental vs Full Update")
    print("=" * 45)
    
    # 1. 测试增量更新
    print(f"\n🚀 Testing Incremental Update:")
    print("-" * 30)
    
    start_time = datetime.now()
    
    # 使用增量更新
    hsgt_data_incremental = get_hsgt_data_for_stock("00001", existing_data=modified_data)
    incremental_fetch_time = (datetime.now() - start_time).total_seconds()
    
    if hsgt_data_incremental is not None and not hsgt_data_incremental.empty:
        print(f"✅ Incremental fetch successful")
        print(f"   Fetched records: {len(hsgt_data_incremental)}")
        print(f"   Fetch time: {incremental_fetch_time:.2f} seconds")
        
        # 显示获取到的日期
        fetched_dates = hsgt_data_incremental['hsgt_date'].tolist()
        print(f"   Fetched dates: {', '.join(fetched_dates[:5])}{'...' if len(fetched_dates) > 5 else ''}")
        
        # 执行增量合并
        merge_start = datetime.now()
        result_incremental = merge_hsgt_data_with_stock_data(modified_data, hsgt_data_incremental, incremental=True)
        incremental_merge_time = (datetime.now() - merge_start).total_seconds()
        
        print(f"   Merge time: {incremental_merge_time:.2f} seconds")
        print(f"   Total time: {incremental_fetch_time + incremental_merge_time:.2f} seconds")
        
        # 检查恢复效果
        recovered_hsgt_count = result_incremental['hsgt_date'].notna().sum()
        original_hsgt_count = original_data['hsgt_date'].notna().sum()
        
        print(f"   HSGT records recovered: {recovered_hsgt_count}/{original_hsgt_count}")
        
    else:
        print(f"❌ Incremental fetch failed or no missing data found")
        result_incremental = modified_data
        incremental_fetch_time = 0
        incremental_merge_time = 0
    
    # 2. 测试全量更新
    print(f"\n🐌 Testing Full Update:")
    print("-" * 25)
    
    start_time = datetime.now()
    
    # 使用全量更新（不传入现有数据）
    hsgt_data_full = get_hsgt_data_for_stock("00001", existing_data=None)
    full_fetch_time = (datetime.now() - start_time).total_seconds()
    
    if hsgt_data_full is not None and not hsgt_data_full.empty:
        print(f"✅ Full fetch successful")
        print(f"   Fetched records: {len(hsgt_data_full)}")
        print(f"   Fetch time: {full_fetch_time:.2f} seconds")
        
        # 执行全量合并
        merge_start = datetime.now()
        result_full = merge_hsgt_data_with_stock_data(modified_data, hsgt_data_full, incremental=False)
        full_merge_time = (datetime.now() - merge_start).total_seconds()
        
        print(f"   Merge time: {full_merge_time:.2f} seconds")
        print(f"   Total time: {full_fetch_time + full_merge_time:.2f} seconds")
        
        # 检查恢复效果
        recovered_hsgt_count_full = result_full['hsgt_date'].notna().sum()
        print(f"   HSGT records recovered: {recovered_hsgt_count_full}/{original_hsgt_count}")
        
    else:
        print(f"❌ Full fetch failed")
        result_full = modified_data
        full_fetch_time = 0
        full_merge_time = 0
    
    # 3. 效率比较
    print(f"\n📊 Efficiency Comparison:")
    print("-" * 25)
    
    if hsgt_data_incremental is not None and hsgt_data_full is not None:
        # 数据量比较
        data_reduction = (len(hsgt_data_full) - len(hsgt_data_incremental)) / len(hsgt_data_full) * 100
        print(f"Data volume reduction: {data_reduction:.1f}%")
        print(f"   Full update: {len(hsgt_data_full)} records")
        print(f"   Incremental: {len(hsgt_data_incremental)} records")
        
        # 时间比较
        total_incremental_time = incremental_fetch_time + incremental_merge_time
        total_full_time = full_fetch_time + full_merge_time
        
        if total_full_time > 0:
            time_reduction = (total_full_time - total_incremental_time) / total_full_time * 100
            print(f"Time reduction: {time_reduction:.1f}%")
            print(f"   Full update: {total_full_time:.2f} seconds")
            print(f"   Incremental: {total_incremental_time:.2f} seconds")
        
        # 网络效率
        print(f"Network efficiency:")
        print(f"   Full update: Downloads all {len(hsgt_data_full)} historical records")
        print(f"   Incremental: Downloads only {len(hsgt_data_incremental)} missing records")
        
        if data_reduction > 50:
            print("✅ Significant efficiency improvement with incremental update!")
        elif data_reduction > 0:
            print("✅ Moderate efficiency improvement with incremental update")
        else:
            print("⚠️  No efficiency gain (all data was needed)")
    
    return result_incremental, result_full

def validate_data_integrity(original_data, result_incremental, result_full):
    """验证数据完整性"""
    
    print(f"\n🔍 Data Integrity Validation:")
    print("-" * 35)
    
    original_hsgt_count = original_data['hsgt_date'].notna().sum()
    incremental_hsgt_count = result_incremental['hsgt_date'].notna().sum()
    full_hsgt_count = result_full['hsgt_date'].notna().sum()
    
    print(f"Original HSGT records: {original_hsgt_count}")
    print(f"Incremental result: {incremental_hsgt_count}")
    print(f"Full update result: {full_hsgt_count}")
    
    # 检查数据一致性
    if incremental_hsgt_count == original_hsgt_count:
        print("✅ Incremental update fully restored HSGT data")
    else:
        print(f"⚠️  Incremental update: {original_hsgt_count - incremental_hsgt_count} records still missing")
    
    if full_hsgt_count == original_hsgt_count:
        print("✅ Full update fully restored HSGT data")
    else:
        print(f"⚠️  Full update: {original_hsgt_count - full_hsgt_count} records still missing")

def cleanup_backup():
    """清理备份文件"""
    backup_file = "/Users/<USER>/AI/Cursor/Futu/data/H_daily/00001_backup.csv"
    if os.path.exists(backup_file):
        os.remove(backup_file)
        print(f"🗑️  Cleaned up backup file")

def main():
    """主函数"""
    print("🔍 Real Data Incremental HSGT Update Test")
    print("=" * 50)
    print(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 创建备份并模拟缺失数据
        original_data, modified_data = create_backup_and_simulate_missing_data()
        
        if original_data is None or modified_data is None:
            print("❌ Failed to prepare test data")
            return
        
        # 2. 测试增量更新 vs 全量更新
        result_incremental, result_full = test_incremental_vs_full_update(original_data, modified_data)
        
        # 3. 验证数据完整性
        validate_data_integrity(original_data, result_incremental, result_full)
        
        print(f"\n🏁 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理备份文件
        cleanup_backup()
    
    print(f"\n💡 Summary:")
    print("The new incremental update mechanism:")
    print("1. ✅ Only fetches data for missing dates")
    print("2. ✅ Preserves existing HSGT data")
    print("3. ✅ Reduces API calls and network usage")
    print("4. ✅ Faster execution for partial updates")
    print("5. ✅ More efficient than full replacement")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试港股通数据类型匹配修复

验证修复后的增量更新是否能正确找到缺失的港股通数据。
"""

import pandas as pd
import sys
import os

# 添加项目路径
sys.path.insert(0, '/Users/<USER>/AI/Cursor/Futu')

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("❌ AKShare not available")
    exit(1)

# 导入修复后的函数
from data.get_daily_data_final import get_hsgt_data_for_stock, merge_hsgt_data_with_stock_data

def create_test_data_with_missing_hsgt():
    """创建有缺失港股通数据的测试数据"""
    
    # 创建最近几天的测试数据
    dates = ['2025-07-28', '2025-07-29', '2025-07-30', '2025-07-31']
    
    test_data = []
    for i, date in enumerate(dates):
        row = {
            'code': 'HK.00001',
            'name': '长和',
            'time_key': f'{date} 00:00:00',
            'open': 125.0 + i,
            'close': 125.5 + i,
            'high': 126.0 + i,
            'low': 124.5 + i,
            'pe_ratio': 11.5,
            'turnover_rate': 0.002,
            'volume': 5000000,
            'turnover': 600000000,
            'change_rate': 0.5,
            'last_close': 125.0 + i - 0.3,
            # 港股通数据字段 - 模拟缺失
            'hsgt_date': None,
            'hsgt_close_price': None,
            'hsgt_change_rate': None,
            'hsgt_holding_shares': None,
            'hsgt_holding_value': None,
            'hsgt_holding_ratio': None,
            'hsgt_value_change_1d': None,
            'hsgt_value_change_5d': None,
            'hsgt_value_change_10d': None
        }
        test_data.append(row)
    
    return pd.DataFrame(test_data)

def test_fixed_incremental_update():
    """测试修复后的增量更新"""
    
    print("🧪 Testing Fixed Incremental HSGT Update")
    print("=" * 45)
    
    # 1. 创建测试数据
    print("📊 Step 1: Creating test data with missing HSGT...")
    test_data = create_test_data_with_missing_hsgt()
    
    print(f"   Created {len(test_data)} records")
    missing_count = test_data['hsgt_date'].isna().sum()
    print(f"   Missing HSGT records: {missing_count}")
    
    # 显示测试数据
    print(f"\n📅 Test data:")
    for _, row in test_data.iterrows():
        date = row['time_key'][:10]
        hsgt_status = "Present" if pd.notna(row['hsgt_date']) else "Missing"
        print(f"   {date}: {hsgt_status}")
    
    # 2. 测试修复后的获取函数
    print(f"\n📡 Step 2: Testing fixed HSGT data fetching...")
    
    hsgt_data = get_hsgt_data_for_stock("00001", existing_data=test_data)
    
    if hsgt_data is not None and not hsgt_data.empty:
        print(f"✅ Successfully retrieved {len(hsgt_data)} HSGT records")
        
        # 显示获取到的数据
        print(f"\n📋 Retrieved HSGT data:")
        for _, row in hsgt_data.iterrows():
            print(f"   {row['hsgt_date']}: {row['hsgt_holding_shares']:,.0f} shares")
        
        # 3. 测试增量合并
        print(f"\n🔄 Step 3: Testing incremental merge...")
        
        merged_data = merge_hsgt_data_with_stock_data(test_data, hsgt_data, incremental=True)
        
        # 检查合并结果
        after_merge_count = merged_data['hsgt_date'].notna().sum()
        print(f"   HSGT records after merge: {after_merge_count}")
        
        # 显示合并后的数据
        print(f"\n📊 Merged data status:")
        for _, row in merged_data.iterrows():
            date = row['time_key'][:10]
            if pd.notna(row['hsgt_date']):
                print(f"   ✅ {date}: HSGT data present ({row['hsgt_holding_shares']:,.0f} shares)")
            else:
                print(f"   ❌ {date}: HSGT data still missing")
        
        # 计算成功率
        success_rate = after_merge_count / missing_count * 100
        print(f"\n📈 Success rate: {success_rate:.1f}% ({after_merge_count}/{missing_count})")
        
        return True
        
    else:
        print(f"❌ Failed to retrieve HSGT data")
        return False

def test_with_real_csv():
    """使用真实CSV数据测试"""
    
    print(f"\n📁 Testing with Real CSV Data")
    print("=" * 35)
    
    csv_file = "/Users/<USER>/AI/Cursor/Futu/data/H_daily/00001.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found")
        return False
    
    # 读取真实数据
    real_data = pd.read_csv(csv_file)
    print(f"📊 Loaded {len(real_data)} records from CSV")
    
    # 检查最近的港股通数据状态
    recent_data = real_data.tail(10)
    missing_dates = []
    
    print(f"\n📅 Recent HSGT status:")
    for _, row in recent_data.iterrows():
        date = row['time_key'][:10]
        if pd.notna(row.get('hsgt_date')):
            print(f"   ✅ {date}: HSGT present")
        else:
            print(f"   ❌ {date}: HSGT missing")
            missing_dates.append(date)
    
    if missing_dates:
        print(f"\n🎯 Found {len(missing_dates)} missing dates: {missing_dates}")
        
        # 测试增量更新
        print(f"\n📡 Testing incremental update on real data...")
        hsgt_data = get_hsgt_data_for_stock("00001", existing_data=real_data)
        
        if hsgt_data is not None and not hsgt_data.empty:
            print(f"✅ Retrieved {len(hsgt_data)} HSGT records for missing dates")
            
            # 显示将要更新的数据
            print(f"\n📋 Data to be updated:")
            for _, row in hsgt_data.iterrows():
                print(f"   {row['hsgt_date']}: {row['hsgt_holding_shares']:,.0f} shares")
            
            return True
        else:
            print(f"❌ No HSGT data retrieved for missing dates")
            return False
    else:
        print(f"\n✅ No missing HSGT dates found in recent data")
        return True

def main():
    """主函数"""
    print("🔍 HSGT Data Type Fix Test")
    print("=" * 30)
    print(f"Test time: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1：模拟数据
    success1 = test_fixed_incremental_update()
    
    # 测试2：真实数据
    success2 = test_with_real_csv()
    
    print(f"\n🏁 Test Results:")
    print("=" * 20)
    print(f"Simulated data test: {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"Real CSV data test: {'✅ PASSED' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        print(f"\n🎉 All tests passed! The data type fix is working correctly.")
        print(f"\n💡 The issue was:")
        print(f"   - hsgt_date column contained datetime.date objects")
        print(f"   - missing_dates was a list of strings")
        print(f"   - Fixed by converting hsgt_date to string for comparison")
    else:
        print(f"\n❌ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()

# 市场宽度分析器运行时配置文件

runtime:
  parallel_processing: true        # 启用并行处理
  cache_intermediate_results: true # 缓存中间结果
  log_level: WARNING              # 日志级别 (DEBUG, INFO, WARNING, ERROR) - 减少输出
  output_format: [csv, json]      # 输出格式

# 性能配置
performance:
  max_workers: 4                  # 最大并行工作进程数
  chunk_size: 1000               # 数据分块大小
  memory_limit_gb: 2             # 内存使用限制(GB)

# 缓存配置
cache:
  enable_disk_cache: true        # 启用磁盘缓存
  cache_directory: .cache        # 缓存目录
  cache_expiry_days: 7           # 缓存过期天数

# 输出配置
output:
  base_directory: output         # 输出基础目录
  include_timestamp: true        # 文件名包含时间戳
  compress_output: false         # 压缩输出文件
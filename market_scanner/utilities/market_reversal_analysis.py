#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场反转分析 - 基于市场宽度指标极值预测市场反转
分析各种市场宽度指标的极值与后续市场表现的关系
"""

import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams
from pathlib import Path
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False


class MarketReversalAnalyzer:
    """市场反转分析器"""
    
    def __init__(self):
        self.data = None
        self.index_data = None
        
    def load_data(self):
        """加载市场宽度数据和指数数据"""
        # 加载市场宽度数据
        breadth_path = project_root / 'market_scanner' / 'results' / 'market_breadth_data_20050104_20250718.csv'
        index_path = project_root / 'market_scanner' / 'results' / 'hs300_vs_cumulative_ad_line_20050104_20250718.csv'
        
        if not breadth_path.exists() or not index_path.exists():
            print("数据文件不存在，请先运行 plot_market_breadth_indicators.py")
            return False
            
        self.data = pd.read_csv(breadth_path, encoding='utf-8-sig')
        self.index_data = pd.read_csv(index_path, encoding='utf-8-sig')
        
        # 转换日期格式
        self.data['date'] = pd.to_datetime(self.data['date'])
        self.index_data['date'] = pd.to_datetime(self.index_data['date'])
        
        # 合并数据
        self.data = self.data.merge(self.index_data[['date', 'close']], on='date', how='left')
        self.data = self.data.rename(columns={'close': 'hs300_close'})
        
        # 计算指数收益率
        self.data['hs300_return'] = self.data['hs300_close'].pct_change()
        
        # 计算未来收益率（用于分析预测效果）
        for period in [5, 10, 20, 60]:
            self.data[f'future_return_{period}d'] = self.data['hs300_return'].shift(-period).rolling(period).sum()
            
        print(f"数据加载完成，共 {len(self.data)} 个交易日")
        return True
        
    def define_extreme_conditions(self):
        """定义各种极值条件"""
        extreme_conditions = {
            # EMA相关极值
            'ema20_extreme_high': self.data['above_ema20_ratio'] >= 0.9,  # 90%以上股票在EMA20之上
            'ema20_extreme_low': self.data['above_ema20_ratio'] <= 0.1,   # 10%以下股票在EMA20之上
            'ema60_extreme_high': self.data['above_ema60_ratio'] >= 0.9,
            'ema60_extreme_low': self.data['above_ema60_ratio'] <= 0.1,
            'ema120_extreme_high': self.data['above_ema120_ratio'] >= 0.9,
            'ema120_extreme_low': self.data['above_ema120_ratio'] <= 0.1,
            
            # McClellan振荡器极值
            'mcclellan_extreme_high': self.data['mcclellan_oscillator'] >= 100,
            'mcclellan_extreme_low': self.data['mcclellan_oscillator'] <= -100,
            'mcclellan_very_high': self.data['mcclellan_oscillator'] >= 150,
            'mcclellan_very_low': self.data['mcclellan_oscillator'] <= -150,
            
            # 涨跌家数比极值
            'advance_decline_extreme_high': self.data['advance_decline_ratio'] >= 0.9,
            'advance_decline_extreme_low': self.data['advance_decline_ratio'] <= 0.1,
            
            # 新高新低比例极值
            'new_highs_extreme_high': self.data['net_new_highs_ratio_20d'] >= 0.3,  # 30%净新高
            'new_highs_extreme_low': self.data['net_new_highs_ratio_20d'] <= -0.3,  # 30%净新低
            
            # 交易量活跃度极值
            'volume_extreme_high': self.data['volume_distribution'] >= 0.6,  # 60%股票交易活跃
            'volume_extreme_low': self.data['volume_distribution'] <= 0.1,   # 10%股票交易活跃
            
            # 组合极值条件
            'bullish_extreme': (self.data['above_ema20_ratio'] >= 0.85) & 
                              (self.data['mcclellan_oscillator'] >= 80) &
                              (self.data['advance_decline_ratio'] >= 0.8),
            'bearish_extreme': (self.data['above_ema20_ratio'] <= 0.15) & 
                              (self.data['mcclellan_oscillator'] <= -80) &
                              (self.data['advance_decline_ratio'] <= 0.2),
        }
        
        return extreme_conditions
        
    def analyze_reversal_signals(self, extreme_conditions):
        """分析极值信号的反转预测能力"""
        results = {}
        
        for condition_name, condition in extreme_conditions.items():
            if condition.sum() == 0:
                continue
                
            # 获取满足条件的日期
            extreme_dates = self.data[condition]
            
            # 分析后续表现
            analysis = {
                'condition_name': condition_name,
                'total_signals': condition.sum(),
                'signal_dates': extreme_dates['date'].tolist(),
                'avg_current_return': extreme_dates['hs300_return'].mean(),
                'future_performance': {}
            }
            
            # 分析不同时间窗口的未来表现
            for period in [5, 10, 20, 60]:
                future_col = f'future_return_{period}d'
                if future_col in extreme_dates.columns:
                    future_returns = extreme_dates[future_col].dropna()
                    if len(future_returns) > 0:
                        analysis['future_performance'][f'{period}d'] = {
                            'mean_return': future_returns.mean(),
                            'median_return': future_returns.median(),
                            'positive_rate': (future_returns > 0).mean(),
                            'std_return': future_returns.std(),
                            'sample_size': len(future_returns)
                        }
            
            results[condition_name] = analysis
            
        return results
        
    def calculate_signal_effectiveness(self, results):
        """计算信号有效性评分"""
        effectiveness_scores = {}
        
        for condition_name, analysis in results.items():
            if not analysis['future_performance']:
                continue
                
            scores = []
            
            # 基于20日未来收益率计算主要评分
            if '20d' in analysis['future_performance']:
                perf_20d = analysis['future_performance']['20d']
                
                # 评分标准：
                # 1. 平均收益率的绝对值（反转幅度）
                # 2. 胜率（正收益率的比例）
                # 3. 样本数量（信号频率）
                # 4. 收益率稳定性（标准差的倒数）
                
                mean_return = abs(perf_20d['mean_return'])
                positive_rate = perf_20d['positive_rate']
                sample_size = min(perf_20d['sample_size'] / 100, 1.0)  # 标准化到0-1
                stability = 1 / (1 + perf_20d['std_return']) if perf_20d['std_return'] > 0 else 0
                
                # 对于看涨极值，期望未来收益为负（反转）
                # 对于看跌极值，期望未来收益为正（反转）
                if 'high' in condition_name or 'bullish' in condition_name:
                    # 看涨极值，期望反转向下
                    reversal_score = mean_return if perf_20d['mean_return'] < 0 else -mean_return
                    win_rate_score = 1 - positive_rate  # 负收益率比例越高越好
                else:
                    # 看跌极值，期望反转向上
                    reversal_score = mean_return if perf_20d['mean_return'] > 0 else -mean_return
                    win_rate_score = positive_rate  # 正收益率比例越高越好
                
                # 综合评分
                total_score = (reversal_score * 0.4 + win_rate_score * 0.3 + 
                              sample_size * 0.2 + stability * 0.1)
                
                effectiveness_scores[condition_name] = {
                    'total_score': total_score,
                    'reversal_score': reversal_score,
                    'win_rate_score': win_rate_score,
                    'sample_size_score': sample_size,
                    'stability_score': stability,
                    'mean_return_20d': perf_20d['mean_return'],
                    'positive_rate_20d': perf_20d['positive_rate'],
                    'sample_count': perf_20d['sample_size']
                }
        
        return effectiveness_scores
        
    def generate_report(self, results, effectiveness_scores):
        """生成分析报告"""
        print("\n" + "="*80)
        print("市场反转信号分析报告")
        print("="*80)
        
        # 按有效性评分排序
        sorted_scores = sorted(effectiveness_scores.items(), 
                              key=lambda x: x[1]['total_score'], reverse=True)
        
        print(f"\n{'指标名称':<25} {'信号数':<8} {'20日收益率':<12} {'胜率':<8} {'有效性评分':<10}")
        print("-" * 80)
        
        for condition_name, scores in sorted_scores:
            if condition_name in results:
                analysis = results[condition_name]
                signal_count = analysis['total_signals']
                mean_return = scores['mean_return_20d']
                positive_rate = scores['positive_rate_20d']
                total_score = scores['total_score']
                
                print(f"{condition_name:<25} {signal_count:<8} {mean_return:<12.4f} "
                      f"{positive_rate:<8.2%} {total_score:<10.4f}")
        
        print("\n" + "="*80)
        print("详细分析结果:")
        print("="*80)
        
        # 详细分析前5个最有效的信号
        for i, (condition_name, scores) in enumerate(sorted_scores[:5]):
            if condition_name not in results:
                continue
                
            analysis = results[condition_name]
            print(f"\n{i+1}. {condition_name}")
            print(f"   信号总数: {analysis['total_signals']}")
            print(f"   当日平均收益率: {analysis['avg_current_return']:.4f}")
            
            for period, perf in analysis['future_performance'].items():
                print(f"   未来{period}表现:")
                print(f"     平均收益率: {perf['mean_return']:.4f}")
                print(f"     中位数收益率: {perf['median_return']:.4f}")
                print(f"     正收益率比例: {perf['positive_rate']:.2%}")
                print(f"     收益率标准差: {perf['std_return']:.4f}")
                print(f"     样本数量: {perf['sample_size']}")
        
        return sorted_scores

    def plot_extreme_signals(self, extreme_conditions, top_signals=5):
        """绘制极值信号与市场表现的关系图"""
        # 获取有效性评分
        results = self.analyze_reversal_signals(extreme_conditions)
        effectiveness_scores = self.calculate_signal_effectiveness(results)

        # 选择前几个最有效的信号
        sorted_scores = sorted(effectiveness_scores.items(),
                              key=lambda x: x[1]['total_score'], reverse=True)

        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('市场宽度指标极值信号分析', fontsize=16, fontweight='bold')

        # 绘制前6个最有效的信号
        for i, (condition_name, scores) in enumerate(sorted_scores[:6]):
            if i >= 6:
                break

            row = i // 3
            col = i % 3
            ax = axes[row, col]

            # 获取信号点
            condition = extreme_conditions[condition_name]
            signal_dates = self.data[condition]['date']
            signal_values = self.data[condition]['hs300_close']

            # 绘制指数走势
            ax.plot(self.data['date'], self.data['hs300_close'], 'b-', alpha=0.7, linewidth=1)

            # 标记信号点
            if 'high' in condition_name or 'bullish' in condition_name:
                # 看涨极值用红色标记（预期反转向下）
                ax.scatter(signal_dates, signal_values, color='red', s=30, alpha=0.8,
                          label=f'信号点 ({len(signal_dates)}个)')
            else:
                # 看跌极值用绿色标记（预期反转向上）
                ax.scatter(signal_dates, signal_values, color='green', s=30, alpha=0.8,
                          label=f'信号点 ({len(signal_dates)}个)')

            ax.set_title(f'{condition_name}\n评分: {scores["total_score"]:.3f}', fontsize=10)
            ax.set_ylabel('沪深300指数')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)

            # 格式化x轴
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
            ax.xaxis.set_major_locator(mdates.YearLocator(5))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图表
        output_path = project_root / 'market_scanner' / 'results' / 'market_reversal_signals.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"信号分析图表已保存到: {output_path}")

        plt.show()

    def run_analysis(self):
        """运行完整分析"""
        print("开始市场反转信号分析...")

        # 加载数据
        if not self.load_data():
            return

        # 定义极值条件
        extreme_conditions = self.define_extreme_conditions()

        # 分析反转信号
        results = self.analyze_reversal_signals(extreme_conditions)

        # 计算有效性评分
        effectiveness_scores = self.calculate_signal_effectiveness(results)

        # 生成报告
        sorted_scores = self.generate_report(results, effectiveness_scores)

        # 绘制图表
        self.plot_extreme_signals(extreme_conditions)

        # 保存详细结果
        self.save_detailed_results(results, effectiveness_scores)

        return results, effectiveness_scores, sorted_scores

    def save_detailed_results(self, results, effectiveness_scores):
        """保存详细分析结果"""
        # 保存有效性评分
        scores_df = pd.DataFrame(effectiveness_scores).T
        scores_path = project_root / 'market_scanner' / 'results' / 'reversal_signal_effectiveness.csv'
        scores_df.to_csv(scores_path, encoding='utf-8-sig')
        print(f"有效性评分已保存到: {scores_path}")

        # 保存信号详情
        signal_details = []
        for condition_name, analysis in results.items():
            if analysis['total_signals'] > 0:
                for date in analysis['signal_dates']:
                    signal_details.append({
                        'condition': condition_name,
                        'date': date,
                        'hs300_close': self.data[self.data['date'] == date]['hs300_close'].iloc[0] if len(self.data[self.data['date'] == date]) > 0 else None
                    })

        if signal_details:
            signals_df = pd.DataFrame(signal_details)
            signals_path = project_root / 'market_scanner' / 'results' / 'reversal_signal_details.csv'
            signals_df.to_csv(signals_path, index=False, encoding='utf-8-sig')
            print(f"信号详情已保存到: {signals_path}")


def main():
    """主函数"""
    analyzer = MarketReversalAnalyzer()
    results, effectiveness_scores, sorted_scores = analyzer.run_analysis()

    print("\n分析完成！")
    print("主要发现:")
    print("1. 查看生成的图表了解各种极值信号的分布")
    print("2. 查看保存的CSV文件了解详细的有效性评分")
    print("3. 重点关注评分最高的几个信号作为市场反转的预警指标")


if __name__ == "__main__":
    main()

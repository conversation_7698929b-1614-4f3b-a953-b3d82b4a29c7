#!/usr/bin/env python3
"""
调试港股通数据过滤问题

检查为什么增量更新找不到缺失日期的数据。
"""

import pandas as pd
import sys
import os

# 添加项目路径
sys.path.insert(0, '/Users/<USER>/AI/Cursor/Futu')

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("❌ AKShare not available")
    exit(1)

def debug_hsgt_data_filtering():
    """调试港股通数据过滤逻辑"""
    
    print("🔍 Debugging HSGT Data Filtering")
    print("=" * 40)
    
    # 1. 获取原始港股通数据
    print("📡 Step 1: Fetching raw HSGT data...")
    hsgt_data = ak.stock_hsgt_individual_em(symbol="00001")
    
    if hsgt_data is None or hsgt_data.empty:
        print("❌ No HSGT data from API")
        return
    
    print(f"✅ Retrieved {len(hsgt_data)} HSGT records")
    
    # 显示原始数据的列名和最新几条记录
    print(f"\n📋 Original columns: {list(hsgt_data.columns)}")
    print(f"\n📅 Latest 5 records from API:")
    latest_5 = hsgt_data.sort_values('持股日期', ascending=False).head(5)
    for i, (_, row) in enumerate(latest_5.iterrows()):
        print(f"  {i+1}. {row['持股日期']} | 持股: {row['持股数量']:,.0f}")
    
    # 2. 处理数据格式
    print(f"\n🔄 Step 2: Processing data format...")
    hsgt_data_processed = hsgt_data.copy()
    hsgt_data_processed['time_key'] = pd.to_datetime(hsgt_data_processed['持股日期']).dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # 重命名列
    hsgt_columns_mapping = {
        '持股日期': 'hsgt_date',
        '当日收盘价': 'hsgt_close_price',
        '当日涨跌幅': 'hsgt_change_rate',
        '持股数量': 'hsgt_holding_shares',
        '持股市值': 'hsgt_holding_value',
        '持股数量占A股百分比': 'hsgt_holding_ratio',
        '持股市值变化-1日': 'hsgt_value_change_1d',
        '持股市值变化-5日': 'hsgt_value_change_5d',
        '持股市值变化-10日': 'hsgt_value_change_10d'
    }
    
    hsgt_data_processed = hsgt_data_processed.rename(columns=hsgt_columns_mapping)
    
    print(f"✅ Data processed, columns: {list(hsgt_data_processed.columns)}")
    
    # 显示处理后的最新几条记录
    print(f"\n📅 Latest 5 processed records:")
    latest_processed = hsgt_data_processed.sort_values('hsgt_date', ascending=False).head(5)
    for i, (_, row) in enumerate(latest_processed.iterrows()):
        print(f"  {i+1}. hsgt_date: '{row['hsgt_date']}' | time_key: '{row['time_key']}'")
    
    # 3. 测试过滤逻辑
    print(f"\n🎯 Step 3: Testing filter logic...")
    
    # 模拟缺失日期
    missing_dates = ['2025-07-30']
    print(f"Missing dates to find: {missing_dates}")
    
    # 检查数据类型
    print(f"\nData types:")
    print(f"  hsgt_date dtype: {hsgt_data_processed['hsgt_date'].dtype}")
    print(f"  missing_dates type: {type(missing_dates[0])}")
    
    # 显示hsgt_date的唯一值（最近10个）
    unique_dates = hsgt_data_processed['hsgt_date'].unique()
    print(f"\nUnique hsgt_date values (latest 10): {sorted(unique_dates, reverse=True)[:10]}")
    
    # 测试过滤
    print(f"\n🔍 Testing filter:")
    missing_dates_filter = hsgt_data_processed['hsgt_date'].isin(missing_dates)
    print(f"Filter result: {missing_dates_filter.sum()} matches found")
    
    if missing_dates_filter.sum() > 0:
        filtered_data = hsgt_data_processed[missing_dates_filter]
        print(f"✅ Found matching records:")
        for _, row in filtered_data.iterrows():
            print(f"  Date: {row['hsgt_date']} | Shares: {row['hsgt_holding_shares']:,.0f}")
    else:
        print(f"❌ No matching records found")
        
        # 检查是否有相似的日期
        print(f"\nChecking for similar dates:")
        for date in missing_dates:
            similar_dates = [d for d in unique_dates if date in str(d)]
            print(f"  Dates containing '{date}': {similar_dates}")
    
    # 4. 测试不同的过滤方法
    print(f"\n🧪 Step 4: Testing alternative filter methods...")
    
    for missing_date in missing_dates:
        print(f"\nTesting filters for '{missing_date}':")
        
        # 方法1：直接匹配
        method1 = hsgt_data_processed['hsgt_date'] == missing_date
        print(f"  Method 1 (exact match): {method1.sum()} matches")
        
        # 方法2：字符串包含
        method2 = hsgt_data_processed['hsgt_date'].str.contains(missing_date, na=False)
        print(f"  Method 2 (contains): {method2.sum()} matches")
        
        # 方法3：日期转换后匹配
        try:
            missing_date_dt = pd.to_datetime(missing_date).strftime('%Y-%m-%d')
            method3 = hsgt_data_processed['hsgt_date'].str.startswith(missing_date_dt)
            print(f"  Method 3 (date prefix): {method3.sum()} matches")
        except:
            print(f"  Method 3: Failed to convert date")
        
        # 显示找到的记录
        for method_name, method_filter in [("Method 1", method1), ("Method 2", method2), ("Method 3", method3 if 'method3' in locals() else pd.Series([False]))]:
            if method_filter.sum() > 0:
                found_records = hsgt_data_processed[method_filter]
                print(f"    {method_name} found:")
                for _, row in found_records.iterrows():
                    print(f"      {row['hsgt_date']} | {row['hsgt_holding_shares']:,.0f} shares")

def test_with_real_csv_data():
    """使用真实的CSV数据测试"""
    
    print(f"\n📁 Testing with real CSV data")
    print("=" * 35)
    
    csv_file = "/Users/<USER>/AI/Cursor/Futu/data/H_daily/00001.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return
    
    # 读取CSV数据
    csv_data = pd.read_csv(csv_file)
    print(f"📊 Loaded {len(csv_data)} records from CSV")
    
    # 检查最近几天的港股通数据状态
    print(f"\n📅 Latest 5 records in CSV:")
    latest_csv = csv_data.tail(5)
    for i, (_, row) in enumerate(latest_csv.iterrows()):
        date = row['time_key'][:10]
        hsgt_status = "✅ Present" if pd.notna(row.get('hsgt_date')) else "❌ Missing"
        print(f"  {i+1}. {date}: {hsgt_status}")
    
    # 找出缺失的日期
    missing_in_csv = []
    for _, row in latest_csv.iterrows():
        if pd.isna(row.get('hsgt_date')) and pd.notna(row.get('time_key')):
            missing_in_csv.append(row['time_key'][:10])
    
    if missing_in_csv:
        print(f"\n🎯 Missing dates in CSV: {missing_in_csv}")
        
        # 测试这些日期是否在API数据中存在
        hsgt_data = ak.stock_hsgt_individual_em(symbol="00001")
        if hsgt_data is not None and not hsgt_data.empty:
            api_dates = hsgt_data['持股日期'].tolist()
            print(f"\n📡 Checking if missing dates exist in API:")
            for missing_date in missing_in_csv:
                if missing_date in api_dates:
                    print(f"  ✅ {missing_date}: Available in API")
                else:
                    print(f"  ❌ {missing_date}: Not available in API")
    else:
        print(f"\n✅ No missing HSGT dates found in CSV")

def main():
    """主函数"""
    print("🔍 HSGT Data Filter Debug")
    print("=" * 30)
    print(f"Debug time: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 调试过滤逻辑
    debug_hsgt_data_filtering()
    
    # 测试真实数据
    test_with_real_csv_data()
    
    print(f"\n🏁 Debug completed!")

if __name__ == "__main__":
    main()

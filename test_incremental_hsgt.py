#!/usr/bin/env python3
"""
测试增量港股通数据更新机制

验证新的智能增量更新功能是否正常工作。
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, '/Users/<USER>/AI/Cursor/Futu')

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("❌ AKShare not available")
    exit(1)

# 导入修改后的函数
from data.get_daily_data_final import get_hsgt_data_for_stock, merge_hsgt_data_with_stock_data

def create_test_data_with_gaps():
    """创建有港股通数据缺口的测试数据"""
    
    # 创建最近10天的测试数据
    dates = pd.date_range(start='2025-07-21', end='2025-07-30', freq='D')
    
    test_data = []
    base_price = 125.0
    
    for i, date in enumerate(dates):
        price = base_price + np.random.normal(0, 1)
        
        row = {
            'code': 'HK.00001',
            'name': '长和',
            'time_key': date.strftime('%Y-%m-%d %H:%M:%S'),
            'open': price - 0.5,
            'close': price,
            'high': price + 1.0,
            'low': price - 1.0,
            'pe_ratio': 11.5,
            'turnover_rate': 0.002,
            'volume': 5000000,
            'turnover': 600000000,
            'change_rate': 0.5,
            'last_close': price - 0.3,
            # 港股通数据字段
            'hsgt_date': None,
            'hsgt_close_price': None,
            'hsgt_change_rate': None,
            'hsgt_holding_shares': None,
            'hsgt_holding_value': None,
            'hsgt_holding_ratio': None,
            'hsgt_value_change_1d': None,
            'hsgt_value_change_5d': None,
            'hsgt_value_change_10d': None
        }
        
        # 模拟部分日期有港股通数据，部分没有
        if i < 6:  # 前6天有数据
            row['hsgt_date'] = date.strftime('%Y-%m-%d')
            row['hsgt_close_price'] = 50.0 + i * 0.5
            row['hsgt_holding_shares'] = 125000000 + i * 100000
            row['hsgt_holding_value'] = 6500000000 + i * 50000000
            row['hsgt_holding_ratio'] = 3.25 + i * 0.01
        # 后4天没有港股通数据（模拟缺失）
        
        test_data.append(row)
    
    return pd.DataFrame(test_data)

def test_incremental_update():
    """测试增量更新功能"""
    
    print("🧪 Testing Incremental HSGT Update Mechanism")
    print("=" * 55)
    
    # 1. 创建有缺口的测试数据
    print("📊 Step 1: Creating test data with HSGT gaps...")
    test_data = create_test_data_with_gaps()
    
    print(f"   Created {len(test_data)} days of data")
    
    # 检查缺失情况
    missing_count = test_data['hsgt_date'].isna().sum()
    present_count = test_data['hsgt_date'].notna().sum()
    
    print(f"   HSGT data present: {present_count} days")
    print(f"   HSGT data missing: {missing_count} days")
    
    # 显示缺失的日期
    missing_dates = test_data[test_data['hsgt_date'].isna()]['time_key'].str[:10].tolist()
    print(f"   Missing dates: {', '.join(missing_dates)}")
    
    # 2. 测试新的智能获取函数
    print(f"\n📡 Step 2: Testing smart HSGT data fetching...")
    
    # 使用新的函数，传入现有数据
    hsgt_data = get_hsgt_data_for_stock("00001", existing_data=test_data)
    
    if hsgt_data is None or hsgt_data.empty:
        print("❌ No HSGT data returned from smart fetch")
        return
    
    print(f"   Smart fetch returned {len(hsgt_data)} records")
    
    # 显示获取到的数据日期
    fetched_dates = hsgt_data['hsgt_date'].tolist()
    print(f"   Fetched dates: {', '.join(fetched_dates)}")
    
    # 3. 测试增量合并
    print(f"\n🔄 Step 3: Testing incremental merge...")
    
    print("   Before incremental merge:")
    before_count = test_data['hsgt_date'].notna().sum()
    print(f"     Records with HSGT data: {before_count}")
    
    # 执行增量合并
    merged_data = merge_hsgt_data_with_stock_data(test_data, hsgt_data, incremental=True)
    
    print("   After incremental merge:")
    after_count = merged_data['hsgt_date'].notna().sum()
    print(f"     Records with HSGT data: {after_count}")
    
    # 4. 验证结果
    print(f"\n✅ Step 4: Validating incremental update results...")
    
    # 检查之前缺失的日期是否被补全
    filled_count = 0
    for date in missing_dates:
        date_data = merged_data[merged_data['time_key'].str.startswith(date)]
        if not date_data.empty and pd.notna(date_data.iloc[0]['hsgt_date']):
            filled_count += 1
            print(f"   ✅ {date}: HSGT data filled")
        else:
            print(f"   ❌ {date}: HSGT data still missing")
    
    # 检查原有数据是否保持不变
    preserved_count = 0
    original_present_dates = test_data[test_data['hsgt_date'].notna()]['time_key'].str[:10].tolist()
    
    for date in original_present_dates:
        original_data = test_data[test_data['time_key'].str.startswith(date)].iloc[0]
        merged_data_row = merged_data[merged_data['time_key'].str.startswith(date)].iloc[0]
        
        if (pd.notna(merged_data_row['hsgt_date']) and 
            original_data['hsgt_holding_shares'] == merged_data_row['hsgt_holding_shares']):
            preserved_count += 1
            print(f"   ✅ {date}: Original HSGT data preserved")
        else:
            print(f"   ⚠️  {date}: Original HSGT data changed")
    
    print(f"\n📊 Incremental Update Summary:")
    print(f"   Originally missing: {missing_count} days")
    print(f"   Successfully filled: {filled_count} days")
    print(f"   Original data preserved: {preserved_count}/{len(original_present_dates)} days")
    print(f"   Fill success rate: {filled_count/missing_count*100:.1f}%")
    print(f"   Preservation rate: {preserved_count/len(original_present_dates)*100:.1f}%")
    
    return merged_data

def compare_with_full_update():
    """比较增量更新和全量更新的效率"""
    
    print(f"\n🔄 Comparing Incremental vs Full Update")
    print("=" * 45)
    
    # 创建测试数据
    test_data = create_test_data_with_gaps()
    
    print("📊 Test scenario:")
    missing_count = test_data['hsgt_date'].isna().sum()
    print(f"   Total records: {len(test_data)}")
    print(f"   Missing HSGT records: {missing_count}")
    
    # 测试增量更新
    print(f"\n🚀 Incremental Update:")
    start_time = datetime.now()
    
    hsgt_data_incremental = get_hsgt_data_for_stock("00001", existing_data=test_data)
    incremental_fetch_time = (datetime.now() - start_time).total_seconds()
    
    if hsgt_data_incremental is not None:
        print(f"   Fetched records: {len(hsgt_data_incremental)}")
        print(f"   Fetch time: {incremental_fetch_time:.2f} seconds")
        
        merge_start = datetime.now()
        merged_incremental = merge_hsgt_data_with_stock_data(test_data, hsgt_data_incremental, incremental=True)
        incremental_merge_time = (datetime.now() - merge_start).total_seconds()
        print(f"   Merge time: {incremental_merge_time:.2f} seconds")
        print(f"   Total time: {incremental_fetch_time + incremental_merge_time:.2f} seconds")
    else:
        print("   ❌ Incremental fetch failed")
        return
    
    # 测试全量更新（模拟）
    print(f"\n🐌 Full Update (simulated):")
    start_time = datetime.now()
    
    hsgt_data_full = get_hsgt_data_for_stock("00001", existing_data=None)  # 不传入现有数据
    full_fetch_time = (datetime.now() - start_time).total_seconds()
    
    if hsgt_data_full is not None:
        print(f"   Fetched records: {len(hsgt_data_full)}")
        print(f"   Fetch time: {full_fetch_time:.2f} seconds")
        
        merge_start = datetime.now()
        merged_full = merge_hsgt_data_with_stock_data(test_data, hsgt_data_full, incremental=False)
        full_merge_time = (datetime.now() - merge_start).total_seconds()
        print(f"   Merge time: {full_merge_time:.2f} seconds")
        print(f"   Total time: {full_fetch_time + full_merge_time:.2f} seconds")
    else:
        print("   ❌ Full fetch failed")
        return
    
    # 效率比较
    print(f"\n📈 Efficiency Comparison:")
    data_reduction = (len(hsgt_data_full) - len(hsgt_data_incremental)) / len(hsgt_data_full) * 100
    time_reduction = ((full_fetch_time + full_merge_time) - (incremental_fetch_time + incremental_merge_time)) / (full_fetch_time + full_merge_time) * 100
    
    print(f"   Data reduction: {data_reduction:.1f}% ({len(hsgt_data_full)} → {len(hsgt_data_incremental)} records)")
    print(f"   Time reduction: {time_reduction:.1f}% ({full_fetch_time + full_merge_time:.2f}s → {incremental_fetch_time + incremental_merge_time:.2f}s)")
    
    if data_reduction > 0:
        print("   ✅ Incremental update is more efficient!")
    else:
        print("   ⚠️  No efficiency gain (all data needed)")

def main():
    """主函数"""
    print("🔍 Incremental HSGT Update Test")
    print("=" * 40)
    print(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试增量更新机制
    merged_data = test_incremental_update()
    
    # 比较效率
    compare_with_full_update()
    
    print(f"\n🏁 Test completed!")
    print("=" * 20)
    
    print(f"\n💡 Key Benefits of Incremental Update:")
    print("1. ✅ Only fetches missing data, not all historical data")
    print("2. ✅ Preserves existing HSGT data, only fills gaps")
    print("3. ✅ Faster execution, less API calls")
    print("4. ✅ More efficient network usage")
    print("5. ✅ Reduced risk of API rate limiting")

if __name__ == "__main__":
    main()

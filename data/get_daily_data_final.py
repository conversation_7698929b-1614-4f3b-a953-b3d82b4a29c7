import os
import sys
import pandas as pd
from futu import *
import datetime
import time
from tqdm import tqdm
import random

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utilities.utils import get_last_futu_trading_day

# 导入AKShare用于港股通数据
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
    print("✅ AKShare imported successfully")
except ImportError:
    AKSHARE_AVAILABLE = False
    print("⚠️  AKShare not available, HSGT data will be skipped")

def should_update_data(existing_file, last_trading_day):
    """
    智能判断是否需要更新数据
    """
    if not os.path.exists(existing_file):
        return True, "File does not exist"

    try:
        df = pd.read_csv(existing_file)
        if df.empty:
            return True, "File is empty"

        # 获取最后一条记录的日期
        last_data_date = df['time_key'].iloc[-1].split(' ')[0]

        # 如果最后数据日期 >= 最近交易日，则不需要更新
        if last_data_date >= last_trading_day:
            return False, f"Up-to-date (data: {last_data_date}, trading: {last_trading_day})"

        # 需要更新
        return True, f"Need update from {last_data_date} to {last_trading_day}"

    except Exception as e:
        return True, f"Error reading file: {e}"

def get_hsgt_data_for_stock(stock_code, existing_data=None, max_retries=3):
    """
    获取指定股票的港股通持仓数据（智能增量更新）

    Args:
        stock_code: 股票代码，如'00700'
        existing_data: 现有股票数据DataFrame，用于判断需要补全的日期
        max_retries: 最大重试次数

    Returns:
        pandas.DataFrame or None: 港股通数据（只包含需要更新的部分）
    """
    if not AKSHARE_AVAILABLE:
        return None

    # 分析现有数据，找出需要补全的日期范围
    missing_dates = []
    if existing_data is not None and not existing_data.empty:
        # 检查最近30天内缺失港股通数据的日期
        recent_data = existing_data.tail(30)  # 只检查最近30天

        for _, row in recent_data.iterrows():
            # 如果有股价数据但没有港股通数据，则需要补全
            if pd.isna(row.get('hsgt_date')) and pd.notna(row.get('time_key')):
                date_str = row['time_key'][:10]  # 提取日期部分
                missing_dates.append(date_str)

        if not missing_dates:
            print(f"No missing HSGT data found for {stock_code}")
            return None

        print(f"Found {len(missing_dates)} missing HSGT dates for {stock_code}: {missing_dates[:5]}{'...' if len(missing_dates) > 5 else ''}")

    for retry in range(max_retries):
        try:
            # 添加延迟避免频率限制
            if retry > 0:
                time.sleep(2 + retry)

            hsgt_data = ak.stock_hsgt_individual_em(symbol=stock_code)

            if hsgt_data is not None and not hsgt_data.empty:
                # 转换日期格式以匹配现有数据
                hsgt_data_processed = hsgt_data.copy()
                hsgt_data_processed['time_key'] = pd.to_datetime(hsgt_data_processed['持股日期']).dt.strftime('%Y-%m-%d %H:%M:%S')

                # 重命名列
                hsgt_columns_mapping = {
                    '持股日期': 'hsgt_date',
                    '当日收盘价': 'hsgt_close_price',
                    '当日涨跌幅': 'hsgt_change_rate',
                    '持股数量': 'hsgt_holding_shares',
                    '持股市值': 'hsgt_holding_value',
                    '持股数量占A股百分比': 'hsgt_holding_ratio',
                    '持股市值变化-1日': 'hsgt_value_change_1d',
                    '持股市值变化-5日': 'hsgt_value_change_5d',
                    '持股市值变化-10日': 'hsgt_value_change_10d'
                }

                hsgt_data_processed = hsgt_data_processed.rename(columns=hsgt_columns_mapping)

                # 如果有缺失日期，只返回这些日期的数据
                if missing_dates:
                    # 转换hsgt_date为字符串格式以便匹配
                    hsgt_data_processed['hsgt_date_str'] = hsgt_data_processed['hsgt_date'].astype(str)

                    # 过滤出缺失日期的数据
                    missing_dates_filter = hsgt_data_processed['hsgt_date_str'].isin(missing_dates)
                    filtered_hsgt_data = hsgt_data_processed[missing_dates_filter]

                    if not filtered_hsgt_data.empty:
                        print(f"Retrieved {len(filtered_hsgt_data)} missing HSGT records for {stock_code}")
                        # 移除临时的字符串列
                        filtered_hsgt_data = filtered_hsgt_data.drop(columns=['hsgt_date_str'])
                        return filtered_hsgt_data[['time_key'] + list(hsgt_columns_mapping.values())]
                    else:
                        print(f"No HSGT data available for missing dates of {stock_code}")
                        print(f"Available dates: {sorted(hsgt_data_processed['hsgt_date_str'].unique(), reverse=True)[:5]}")
                        print(f"Looking for: {missing_dates}")
                        return None
                else:
                    # 如果没有指定缺失日期，返回最近的数据（向后兼容）
                    return hsgt_data_processed[['time_key'] + list(hsgt_columns_mapping.values())]
            else:
                return None

        except Exception as e:
            if retry < max_retries - 1:
                print(f"HSGT data fetch failed for {stock_code} (attempt {retry + 1}): {e}")
                continue
            else:
                print(f"HSGT data fetch failed for {stock_code} after {max_retries} attempts: {e}")
                return None

    return None

def merge_hsgt_data_with_stock_data(stock_data, hsgt_data, incremental=True):
    """
    将港股通数据合并到股票数据中（支持增量更新）

    Args:
        stock_data: 股票价格数据DataFrame
        hsgt_data: 港股通数据DataFrame（可以是增量数据）
        incremental: 是否使用增量更新模式

    Returns:
        pandas.DataFrame: 合并后的数据
    """
    hsgt_columns = ['hsgt_date', 'hsgt_close_price', 'hsgt_change_rate',
                   'hsgt_holding_shares', 'hsgt_holding_value', 'hsgt_holding_ratio',
                   'hsgt_value_change_1d', 'hsgt_value_change_5d', 'hsgt_value_change_10d']

    # 确保股票数据有港股通列
    stock_data_copy = stock_data.copy()
    for col in hsgt_columns:
        if col not in stock_data_copy.columns:
            stock_data_copy[col] = None

    if hsgt_data is None or hsgt_data.empty:
        return stock_data_copy

    try:
        if incremental:
            # 增量更新模式：只更新有新数据的行，保留现有数据
            print(f"Performing incremental HSGT data update...")

            # 为每个港股通数据记录更新对应的股票数据行
            for _, hsgt_row in hsgt_data.iterrows():
                hsgt_date = hsgt_row['hsgt_date']

                # 找到对应日期的股票数据行
                matching_rows = stock_data_copy['time_key'].str.startswith(hsgt_date)

                if matching_rows.any():
                    # 更新港股通数据
                    for col in hsgt_columns:
                        if col in hsgt_row:
                            stock_data_copy.loc[matching_rows, col] = hsgt_row[col]

                    print(f"Updated HSGT data for {hsgt_date}")

            return stock_data_copy

        else:
            # 传统的完全替换模式
            print(f"Performing full HSGT data replacement...")

            # 先清空现有的港股通数据
            for col in hsgt_columns:
                stock_data_copy[col] = None

            # 基于日期合并新的港股通数据
            merged_data = pd.merge(
                stock_data_copy.drop(columns=hsgt_columns),
                hsgt_data,
                on='time_key',
                how='left'
            )

            return merged_data

    except Exception as e:
        print(f"Error merging HSGT data: {e}")
        # 如果合并失败，返回原始数据并确保有港股通列
        return stock_data_copy

def get_incremental_history_kline(quote_ctx, code, start_date, max_retries=3):
    """
    获取指定日期范围的历史K线数据，带重试机制
    """
    all_data = pd.DataFrame()
    page_req_key = None
    
    end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    
    while True:
        retry_count = 0
        while retry_count < max_retries:
            try:
                # 添加固定延迟，确保不超过频率限制
                time.sleep(0.6)
                
                ret, data, page_req_key = quote_ctx.request_history_kline(
                    code,
                    start=start_date,
                    end=end_date,
                    ktype=KLType.K_DAY, 
                    autype=AuType.HFQ,
                    max_count=1000, 
                    page_req_key=page_req_key
                )
                
                if ret == RET_OK:
                    if data is not None and not data.empty:
                        all_data = pd.concat([all_data, data], ignore_index=True)
                    else:
                        return all_data  # 没有更多数据
                    break  # 成功，跳出重试循环
                else:
                    if "频率太高" in str(data) or "frequency" in str(data).lower():
                        wait_time = 10 + (retry_count + 1) * 5
                        print(f"Rate limit hit for {code}, waiting {wait_time}s before retry {retry_count + 1}/{max_retries}")
                        time.sleep(wait_time)
                        retry_count += 1
                    else:
                        print(f"Error getting kline for {code}: {data}")
                        return None
                
            except Exception as e:
                if "频率太高" in str(e) or "frequency" in str(e).lower():
                    wait_time = 10 + (retry_count + 1) * 5
                    print(f"Rate limit exception for {code}, waiting {wait_time}s before retry {retry_count + 1}/{max_retries}")
                    time.sleep(wait_time)
                    retry_count += 1
                else:
                    print(f"Exception getting kline for {code}: {e}")
                    return None
        
        if retry_count >= max_retries:
            print(f"Max retries exceeded for {code}")
            return None
            
        if page_req_key is None:
            break
            
    return all_data

def process_single_stock_final(quote_ctx, stock_code, output_dir, last_trading_day):
    """
    最终版本：智能处理单只股票的下载任务，包含港股通数据
    """
    try:
        futu_code = f"HK.{stock_code}"
        output_path = os.path.join(output_dir, f"{stock_code}.csv")

        # 智能判断是否需要更新
        need_update, reason = should_update_data(output_path, last_trading_day)

        if not need_update:
            return f"{futu_code}: {reason}, skipped"

        # 需要更新数据
        stock_data_updated = False
        combined_data = None

        if os.path.exists(output_path):
            # 增量更新
            try:
                existing_df = pd.read_csv(output_path)
                last_data_date = existing_df['time_key'].iloc[-1].split(' ')[0]

                # 计算需要更新的起始日期
                last_datetime = datetime.datetime.strptime(last_data_date, '%Y-%m-%d')
                start_date = (last_datetime + datetime.timedelta(days=1)).strftime('%Y-%m-%d')

                print(f"{futu_code}: Incremental update from {start_date}")

                # 获取增量数据
                new_data = get_incremental_history_kline(quote_ctx, futu_code, start_date)

                if new_data is not None and not new_data.empty:
                    # 合并新旧数据
                    combined_data = pd.concat([existing_df, new_data], ignore_index=True)
                    combined_data.drop_duplicates(subset='time_key', keep='last', inplace=True)
                    combined_data.sort_values(by='time_key', inplace=True)
                    stock_data_updated = True
                else:
                    # 没有新的股价数据，但仍需检查港股通数据
                    combined_data = existing_df.copy()

            except Exception as e:
                print(f"Error in incremental update for {futu_code}: {e}")
                # 如果增量更新失败，重新下载所有数据
                combined_data = None

        if combined_data is None:
            # 全量下载
            print(f"{futu_code}: Full download - {reason}")
            start_date = '1990-01-01'
            history_data = get_incremental_history_kline(quote_ctx, futu_code, start_date)

            if history_data is not None and not history_data.empty:
                # 数据清理
                history_data.drop_duplicates(subset='time_key', keep='last', inplace=True)
                history_data.sort_values(by='time_key', inplace=True)
                combined_data = history_data
                stock_data_updated = True
            else:
                return f"{futu_code}: No data retrieved"

        # 获取港股通数据并合并（智能增量更新）
        hsgt_data = get_hsgt_data_for_stock(stock_code, existing_data=combined_data)
        combined_data = merge_hsgt_data_with_stock_data(combined_data, hsgt_data, incremental=True)

        # 保存更新后的数据
        combined_data.to_csv(output_path, index=False)

        # 构建返回消息
        if stock_data_updated:
            hsgt_msg = f" + HSGT data ({len(hsgt_data)} records)" if hsgt_data is not None and not hsgt_data.empty else " + HSGT columns added"
            if 'Downloaded' in reason or 'Full download' in reason:
                return f"{futu_code}: Downloaded {len(combined_data)} records{hsgt_msg}"
            else:
                return f"{futu_code}: Updated with new records{hsgt_msg}"
        else:
            hsgt_msg = f" + HSGT data updated ({len(hsgt_data)} records)" if hsgt_data is not None and not hsgt_data.empty else ""
            return f"{futu_code}: Stock data up-to-date{hsgt_msg}"

    except Exception as e:
        return f"{stock_code}: Error - {str(e)}"

def consolidate_to_parquet(csv_dir, output_path):
    """
    将CSV文件整合为Parquet
    """
    import glob
    
    csv_files = glob.glob(os.path.join(csv_dir, '*.csv'))
    if not csv_files:
        print(f"No CSV files found in {csv_dir}")
        return
    
    print(f"Consolidating {len(csv_files)} CSV files...")
    all_dataframes = []
    
    for csv_file in tqdm(csv_files, desc="Reading CSV files"):
        try:
            stock_code = os.path.basename(csv_file).replace('.csv', '')
            df = pd.read_csv(csv_file)
            
            if df.empty:
                continue
                
            df['stock_code'] = stock_code
            df['time_key'] = pd.to_datetime(df['time_key'])
            all_dataframes.append(df)
            
        except Exception as e:
            print(f"Error processing {csv_file}: {e}")
            continue
    
    if all_dataframes:
        print("Merging all data...")
        consolidated_df = pd.concat(all_dataframes, ignore_index=True)
        consolidated_df.sort_values(['stock_code', 'time_key'], inplace=True)
        
        print("Saving to Parquet...")
        consolidated_df.to_parquet(output_path, index=False, compression='snappy')
        print(f"✅ Consolidated data saved to {output_path}")
        print(f"Total records: {len(consolidated_df):,}, Stocks: {consolidated_df['stock_code'].nunique()}")

def main():
    """
    主函数 - 使用富途交易日历的智能增量更新
    """
    # 获取脚本绝对路径，确保路径正确
    script_path = os.path.abspath(__file__)
    script_dir = os.path.dirname(script_path)
    project_root = os.path.dirname(script_dir)
    
    print(f"Script path: {script_path}")
    print(f"Project root: {project_root}")
    
    # 定义所有路径
    stock_list_path = os.path.join(project_root, 'data', 'H_list')
    output_dir = os.path.join(project_root, 'data', 'H_daily')
    parquet_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    
    print(f"Stock list path: {stock_list_path}")
    print(f"Output directory: {output_dir}")
    
    # 检查股票列表文件是否存在
    if not os.path.exists(stock_list_path):
        print(f"❌ Error: Stock list file not found at {stock_list_path}")
        print("Please ensure the H_list file exists in the correct location.")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取股票代码
    try:
        with open(stock_list_path, 'r', encoding='utf-8') as f:
            stock_codes = [line.strip() for line in f if line.strip()]
        print(f"✅ Loaded {len(stock_codes)} stock codes")
    except Exception as e:
        print(f"❌ Error reading stock list: {e}")
        return
    
    print("📅 Using Futu trading calendar for smart updates...")
    
    # 创建单个连接用于所有下载
    quote_ctx = None
    try:
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        print("✅ Connected to Futu API")
        
        # 获取最近的交易日
        last_trading_day = get_last_futu_trading_day(quote_ctx, 'HK', 30)
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        
        print(f"📅 Today: {today}")
        print(f"📈 Last trading day: {last_trading_day}")
        
        if last_trading_day == today:
            print("✅ Today is a trading day")
        else:
            print(f"ℹ️  Today is not a trading day")
            print(f"🔍 Will check if data is updated to {last_trading_day}")
        
        results = []
        # 顺序处理每只股票
        for stock_code in tqdm(stock_codes, desc="Processing stocks"):
            result = process_single_stock_final(quote_ctx, stock_code, output_dir, last_trading_day)
            results.append(result)
            
            # 每处理10只股票后稍作休息
            if len(results) % 10 == 0:
                time.sleep(2)
                
    except Exception as e:
        print(f"❌ Error connecting to Futu API: {e}")
        return
    finally:
        if quote_ctx:
            quote_ctx.close()
    
    # 统计结果
    success_count = sum(1 for result in results if ("Updated" in result or "Downloaded" in result or "Up-to-date" in result))
    error_count = sum(1 for result in results if "Error" in result)
    skipped_count = sum(1 for result in results if "skipped" in result)
    hsgt_count = sum(1 for result in results if "HSGT" in result)

    print(f"\n✅ Processing completed!")
    print(f"Successfully processed: {success_count}/{len(stock_codes)} stocks")
    print(f"Skipped (up-to-date): {skipped_count}")
    print(f"With HSGT data: {hsgt_count}")
    print(f"Errors: {error_count}")

    # 显示部分结果
    print("\nSample results:")
    for result in results[:10]:
        print(f"  {result}")
    if len(results) > 10:
        print(f"  ... and {len(results) - 10} more")
    
    # 整合为Parquet文件
    print("\n" + "="*50)
    print("Starting consolidation to Parquet...")
    consolidate_to_parquet(output_dir, parquet_path)
    print("="*50)
    print("🎉 All tasks completed!")

if __name__ == "__main__":
    main()

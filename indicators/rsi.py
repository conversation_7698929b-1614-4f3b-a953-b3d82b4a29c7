import pandas as pd

def rsi(series: pd.Series, period: int = 14) -> pd.Series:
    """
    计算RSI指标
    参数：
        series: pd.Series，通常为收盘价
        period: 计算周期，默认14
    返回：
        rsi: pd.Series，RSI值
    """
    delta = series.diff()
    gain = delta.where(delta > 0, 0.0)
    loss = -delta.where(delta < 0, 0.0)
    avg_gain = gain.ewm(alpha=1/period, min_periods=period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/period, min_periods=period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi
